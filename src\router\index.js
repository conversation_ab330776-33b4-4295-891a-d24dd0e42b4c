import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import { checkToken } from '@/utils/tools'
import tools from '@/utils/tools'
import { getJtToken } from '@/service/api'
import common from '@/utils/common'
import { showToast } from 'vant'

const router = createRouter({
    history: createWebHashHistory(),
    routes: [
        {
            path: '/',
            redirect: '/home'
        },
        {
            path: '/home',
            name: 'home',
            component: Home,
            meta: {
                index: 1
            }
        },
        {
            path: '/login',
            name: 'login',
            component: () => import('@/views/Login.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/user',
            name: 'user',
            component: () => import('@/views/User.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/product-list',
            name: 'product-list',
            component: () => import('@/views/ProductList.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/category',
            name: 'category',
            component: () => import('@/views/Category.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/product/:id',
            name: 'product',
            component: () => import('@/views/ProductDetail.vue'),
            meta: {
                index: 3
            }
        },
        {
            path: '/cart',
            name: 'cart',
            component: () => import('@/views/Cart.vue'),
            meta: {
                index: 1
            }
        },
        {
            path: '/create-order',
            name: 'create-order',
            component: () => import('@/views/CreateOrder.vue'),
            meta: {
                index: 2
            }
        },
        // {
        //   path: '/address',
        //   name: 'address',
        //   component: () => import('@/views/Address.vue'),
        //   meta: {
        //     index: 2
        //   }
        // },
        // {
        //   path: '/address-edit',
        //   name: 'address-edit',
        //   component: () => import('@/views/AddressEdit.vue'),
        //   meta: {
        //     index: 3
        //   }
        // },
        {
            path: '/order/:status',
            name: 'order',
            component: () => import('@/views/Order.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/Orderpack/:status',
            name: 'Orderpack',
            component: () => import('@/views/OrderServicepack.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/order-detail/:id',
            name: 'order-detail',
            component: () => import('@/views/OrderDetail.vue'),
            meta: {
                index: 3
            }
        },
        {
            path: '/order-detail-pack/:id',
            name: 'order-detail-pack',
            component: () => import('@/views/OrderDetailPack.vue'),
            meta: {
                index: 3
            }
        },
        {
            path: '/order-detail-refund/:id',
            name: 'order-detail-refund',
            component: () => import('@/views/OrderDetailrefund.vue'),
            meta: {
                index: 4
            }
        },
        {
            path: '/order-confirm/:id',
            name: 'order-confirm',
            component: () => import('@/views/OrderConfirm.vue'),
            meta: {
                index: 3
            }
        },
        {
            path: '/setting',
            name: 'setting',
            component: () => import('@/views/Setting.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/about',
            name: 'about',
            component: () => import('@/views/About.vue'),
            meta: {
                index: 2
            }
        },
        {
            path: '/service-package/:id',
            name: 'service-package',
            component: () => import('@/views/ServicePackageDetail.vue'),
            meta: {
                index: 3
            }
        },
        {
            path: '/service-package-test',
            name: 'service-package-test',
            component: () => import('@/views/ServicePackageTest.vue'),
            meta: {
                index: 2
            }
        },
        //   {
        //     path: '/:pathMatch(.*)*',
        //     component: () => import('@/views/error-page/404')
        // }

    ]
})

// 不需要登录的白名单路由
const whiteList = ['/login', '/home', '/category', '/product/:id', '/service-package-direct/:id', '/service-package-test'];

router.beforeEach((to, from, next) => {
    const matchedPath = whiteList.some(path => {
        const regex = new RegExp(`^${path.replace(/:\w+/g, '\\w+')}$`);
        return regex.test(to.path);
    });
    // 对/category页面做特殊处理
    if (to.path === '/category') {
        console.log("不需要登录的category");
        let origin = common.getUrlParam("origin");
        let authToken = common.getUrlParam("authToken");
        
        if (authToken && origin) {
            console.log("category页面检测到authToken和origin参数，尝试获取用户信息");
            getJtToken({
                authToken: authToken,
                channelId: origin
            }).then(res => {
                console.log('authtoken获取用户信息getJtToken', res);
                if (res) {
                    window.localStorage.setItem("userInfo", JSON.stringify(tools.mergeUser(res)));
                    window.localStorage.setItem("wxAppid", res.appId|| res.wxAppid);
                    window.localStorage.setItem("naliWxEncrUser", res.encrypt)
                    window.localStorage.setItem("wxOpenid", res.thirdId || res.wxOpenid);
                    window.localStorage.setItem("alipayUserId", res.thirdId || res.wxOpenid);
                    window.localStorage.setItem("encrUserMini", res.encrypt)
                    window.sessionStorage.setItem("encrUserMini", res.encrypt)
                }
                next();
            }).catch(() => {
                next();
            });
        } else {
            next();
        }
    } else if (matchedPath) {
        console.log("不需要登录的白名单路由");
        next();
    } else {
        if (checkToken()) {
            next();
        } else {
            let origin = common.getUrlParam("origin");
            let authToken = common.getUrlParam("authToken");
            console.log("authToken", authToken);
            if (!checkToken()) {
                console.log("触发", checkToken());
                if (authToken && origin) {
                    getJtToken({
                        authToken: authToken,
                        channelId: origin
                    }).then(res => {
                        console.log('authtoken获取用户信息getJtToken', res);
                        if (res) {
                            window.localStorage.setItem("userInfo", JSON.stringify(tools.mergeUser(res)));
                            window.localStorage.setItem("wxAppid", res.appId|| res.wxAppid);
                            window.localStorage.setItem("naliWxEncrUser", res.encrypt)
                            window.localStorage.setItem("wxOpenid", res.thirdId || res.wxOpenid);
                            window.localStorage.setItem("alipayUserId", res.thirdId || res.wxOpenid);
                            window.localStorage.setItem("encrUserMini", res.encrypt)
                            window.sessionStorage.setItem("encrUserMini", res.encrypt)
                            next()
                        } else {
                            console.log("index未获取到authToken");
                            showToast('res未获取到用户信息')
                            // router.push('/login')
                        }
                    });
                } else {
                    console.log("index未获取到authToken");
                    showToast('未获取未获取到authToken')
                    // router.push('/login')
                }
            }
        }
    }
})

const goToHistory = () => {
  // 跳转到历史服务包页面，路径根据你的路由实际情况调整
  router.push({ path: '/servicepack-history' })
}

export default router
