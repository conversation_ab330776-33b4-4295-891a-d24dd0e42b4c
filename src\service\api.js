export const getJtToken = (data) => {
    return fetch(
        //本地用这个
        // "https://jsbceshi.hfi-health.com:18188/hzAppMS/Core/thirdAccess/getJtToken",
        //正式用这个
        import.meta.env.VITE_API_URL || '' + '/hzAppMS/Core/thirdAccess/getJtToken',
        {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        },
        // 查看本地用的环境和域名
        console.log('当前环境:', import.meta.env.MODE),
        console.log('当前VITE_API_URL:', import.meta.env.VITE_API_URL)
    ).then(response => response.json())
     .then(data => {
        if (data.success == 1) {
            return data.value;
        } else {
            let message = data.respDesc || "网络连接超时，请稍后再试~";
            // util.showToast(message);
            return null;
        }
    });
};



export const userAuthorize = (data) => {
     return fetch(
  "https://jsbceshi.hfi-health.com:18188/hzAppMS/Core/UserAuthorize",
   {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
 },
 
 ).then(response => response.json())
 .then(data => {
    if (data.success == 1) {
        return data.value;
    } else {
        let message = data.respDesc || "网络连接超时，请稍后再试~";
        // util.showToast(message);
        return null;
    }
});
    };