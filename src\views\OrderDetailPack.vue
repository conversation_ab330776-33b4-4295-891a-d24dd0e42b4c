<!--
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 *
-->

<template>
  <div class="order-detail-box">
    <!-- <s-header :name="'订单详情'" @callback="close"></s-header> -->
    <!-- <div class="order-status">
      <div class="status-item">
        <label>订单状态：</label>
        <span>{{ state.detail.orderStatusString }}</span>
      </div>
      <div class="status-item">
        <label>订单编号：</label>
        <span>{{ state.detail.orderNo }}</span>
      </div>
      <div class="status-item">
        <label>下单时间：</label>
        <span>{{ state.detail.createTime }}</span>
      </div>
      <van-button v-if="state.detail.orderStatus == 3" style="margin-bottom: 10px" color="#1baeae" block @click="handleConfirmOrder(state.detail.orderNo)">确认收货</van-button>
      <van-button v-if="state.detail.orderStatus == 0" style="margin-bottom: 10px" color="#1baeae" block @click="showPayFn">去支付</van-button>
      <van-button v-if="!(state.detail.orderStatus < 0 || state.detail.orderStatus == 4)" block @click="handleCancelOrder(state.detail.orderNo)">取消订单</van-button>
    </div> -->
    <!-- <div class="order-price">
      <div class="price-item">
        <label>商品金额：</label>
        <span>¥ {{ state.detail.totalPrice }}</span>
      </div>
      <div class="price-item">
        <label>配送方式：</label>
        <span>普通快递</span>
      </div>
    </div> -->
    <div class="order-detail-bg">
      <div class="order-detail-main">
        <!-- 支付状态和倒计时 -->
        <div >
          <van-cell
            class="pay-status-cell"
            
            :label="getLabelText"
            title-style="color: #000000; font-size: 16px;"
            :border="false">
            <template #title>
    <div class="custom-title">
      <img 
:src="getStatusIcon"

      class="title-icon" alt="时钟图标" />
      <span>{{ 
        state.detail.orderStatus === 0 ? '待支付' :
        state.detail.orderStatus === -2 ? '已取消' :
        state.detail.remainTime === 0 ? '已过期' :
        state.detail.goodsServiceIsBlock === true ? '已停用' :
        (state.detail.orderStatus === 2 || state.detail.orderStatus === 3 || state.detail.orderStatus === 6) ? '待使用' :
        (state.detail.orderStatus === 4 || state.detail.orderStatus === 5) ? '已使用' :
        (state.detail.orderStatus === -3 && state.detail.payStatus === -1) ? '退款成功' : ''
      }}</span>
    </div>
  </template>
            <template #label>
    <span v-html="getLabelText"></span>
  </template>
          </van-cell>
        </div>

        <!-- 服务包列表 -->
        <div class="card" style="margin-top: -40px;">
          <div class="order-list-title">{{state.detail.goodsName}} ：</div>
          <!-- <van-divider style="margin: 8px 0 0 0;" /> -->
          <van-cell
            v-for="item in state.detail.fwbDetailVOS"
            :key="item.id"
            :border="false"
            class="order-list-item"
            @click="redirectToService(item.serviceRedirectUrl)"
          >
            <template v-if="item.serviceTypeName==='在线咨询'||item.serviceTypeName==='电话咨询'||item.serviceTypeName==='在线复诊'" #title>
              <div  class="item-left">
                <img :src="item.goodsCoverImg" class="item-img" />
                <div class="item-info">
                  <div class="item-name-row">
                    <span 
                      class="item-name" 
                      :class="{ 'force-wrap': item.doctorName.length > 13 }"
                      :style="item.doctorName.length > 13 ? { 'min-width': '200px' } : {}"
                    >
                      {{ item.doctorName }}
                    </span>
                    <span class="position-tag">{{ item.staffTechnicalTitleName }}</span>
                  </div>
                  <div class="item-desc">{{ item.deptName }}</div>
                </div>
              </div>
            </template>
            <template v-if="item.serviceTypeName==='在线咨询'||item.serviceTypeName==='电话咨询'||item.serviceTypeName==='在线复诊'" #value>
              <div class="item-right">
                <div class="item-type">{{ item.ServiceTypeName }}</div>
                <div class="item-count">*{{ item.remainCount }}</div>
              </div>
            </template>
            <template v-if="item.serviceTypeName==='商品'" #title>
              <div class="item-left">
                <img :src="item.goodsCoverImg" class="item-img" />
                <div class="item-info">
                  <div class="item-name-row">
                    <span 
                      class="item-name" 
                      :class="{ 'force-wrap': item.serviceName.length > 13 }"
                      :style="item.serviceName.length > 13 ? { 'min-width': '200px' } : {}"
                    >
                      {{ item.serviceName }}
                    </span>
                    <!-- <span class="position-tag">{{ item.staffTechnicalTitleName }}</span> -->
                  </div>
                  <div class="item-desc">{{ item.serviceSku }}</div>
                </div>
              </div>
            </template>
            <template v-if="item.serviceTypeName==='商品'" #value>
              <div  class="item-right">
                <div class="item-type" style="min-width: 10px; display: inline-block;">{{ someText || '\u00A0' }}</div>
                <div class="item-count">*{{ item.remainCount }}</div>
              </div>
            </template>
            <template v-if="item.serviceTypeName==='第三方服务'" #title>
              <div  class="item-left">
                <!-- <img :src="item.goodsCoverImg" class="item-img" /> -->
                <div class="item-info">
                  <div class="item-name-row">
                    <span 
                      class="item-name" 
                      :class="{ 'force-wrap': item.serviceName.length > 13 }"
                      :style="item.serviceName.length > 13 ? { 'min-width': '200px' } : {}"
                    >
                      {{ item.serviceName }}
                    </span>
                    <!-- <span class="position-tag">{{ item.staffTechnicalTitleName }}</span> -->
                  </div>
                  <!-- <div class="item-desc">{{ item.serviceSku }}</div> -->
                </div>
              </div>
            </template>
            <template v-if="item.serviceTypeName==='第三方服务'" #value>
              <div  class="item-right">
                <!-- <div class="item-type">{{ item.ServiceTypeName }}</div> -->
                {{ item.serviceUsageCount === item.writeOffCount ? '已使用' : '待使用' }}
              </div>
            </template>
          </van-cell>
          <!-- <van-cell 
          v-if="state.detail.orderStatus === -3 || state.detail.orderStatus === -1 || state.detail.orderStatus === -2"
            :title="项目名称项目名称项目名称项目名称项目名称项目名称"
            title-style="min-width: 250px;color: #333333; font-size: 14px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;"
            style="background: #F8FDFC;border-radius: 4px;margin-left: 15px;margin-right: 15px;margin-bottom: 5px;"
            value-class="custom-value" 
            value="*1"
            :border="false"
          /> -->
        </div>

        <!-- <div class="card"> -->
          <img 

          v-if="state.detail.orderStatus === 2 || state.detail.orderStatus === 3 || state.detail.orderStatus === 6"
            src="@/assets/icon-bgline.png" 
            class="item-img-line"
            alt="divider line"
          />
        <!-- </div> -->
        <div 
        
        v-if="state.detail.orderStatus === 2 || state.detail.orderStatus === 3 || state.detail.orderStatus === 6"

        class="card" style="margin-top: -15px;">
          <van-button
            class="verify-btn"
            block
            round
            color="#01CDA7"
            @click="showQRPopup(state.detail.orderNo)"
          >
            点击查看核销码
          </van-button>

        </div>
        <!-- 服务描述 -->
        <div class="card">
          <van-cell 
            title="服务描述："
            title-style="color: ##333333; font-size: 14px;"
            value-class="custom-value" 
            :border="false"
          />
          <van-cell 
          v-html="state.detail.goodsDetailContent"
            title-style="color: #666; font-size: 14px;"
            style="background: #F8FDFC;border-radius: 4px;margin-left: 10px;margin-right: 10px;margin-top: -5px;margin-bottom: 5px;"
            value-class="custom-value" 
            :border="false"
          />
        </div>

  

        <!-- 订单信息 -->
        <div class="card">
          <van-cell 
          class="custom-cell"
            title="订单编号"
            :value="state.detail.orderNo"
            title-style="color: #666; font-size: 14px;"
            value-class="custom-value" 
            
            :border="false"
          />
          <van-cell 
           class="custom-cell"
            v-if="state.detail.orderStatus!== -3"
            title="创建时间"
            style="margin-top: -10px;"
            :value="state.detail.createTime"
            title-style="color: #666; font-size: 14px;"
            value-class="custom-value" 
            :border="false"
          />
          <van-cell 
           class="custom-cell"
            v-if="state.detail.orderStatus=== 2||state.detail.orderStatus=== 3||state.detail.orderStatus=== 6||state.detail.orderStatus=== 4||state.detail.orderStatus=== 5"
            title="下单时间"
            :value="state.detail.payTime"
            style="margin-top: -10px;"
            title-style="color: #666; font-size: 14px;"
            value-class="custom-value" 
            :border="false"
          />
         
          <van-cell 
           class="custom-cell"
            v-if="state.detail.orderStatus=== 2||state.detail.orderStatus=== 3||state.detail.orderStatus=== 6||state.detail.orderStatus=== 4||state.detail.orderStatus=== 5"
            title="支付流水号"
            style="margin-top: -10px;"
            :title-style="{ 'max-width': '80px','color': '#666','font-size': '14px' }" 
            :border="false"
            value-class="custom-value"
          >
            <template #value>
              <div style="display: flex; align-items: center; justify-content: flex-end;">
                <span style="white-space: nowrap;">{{ state.detail.payOrderNo }}</span>
                <span style="margin: 0 4px; color: #999;">|</span>
                <span 
                  style="color: #3EBFA0; cursor: pointer;" 
                  @click="copyPayOrderNo"
                >复制</span>
              </div>
            </template>
          </van-cell>
          <van-cell 
           class="custom-cell"
            v-if="state.detail.orderStatus=== 2||state.detail.orderStatus=== 3||state.detail.orderStatus=== 6||state.detail.orderStatus=== 4||state.detail.orderStatus=== 5"
            title="支付金额"
            style="margin-top: -10px;"
            title-style="color: #666; font-size: 14px;"
            :border="false"
          >
            <template #value>
              <div style="color: #333333; font-size: 12px;">
                ¥ 
                <span style="font-size: 16px; color: #333;">{{ formatPricePartsSmart(state.detail.totalPrice).int }}</span>
                <span style="font-size: 12px; color: #333;">{{ formatPricePartsSmart(state.detail.totalPrice).dec }}</span>
              </div>
            </template>
          </van-cell>
          <van-cell 
            v-if="false"
            title="退款编号"
            :value="state.detail.refundSeq"
            title-style="color: #666; font-size: 14px;"
            value-class="custom-value" 
            :border="false"
          />
          <van-cell 
            v-if="false"
            title="申请时间"
            :value="state.detail.refundTime"
            title-style="color: #666; font-size: 14px;"
            value-class="custom-value" 
            :border="false"
          />
        </div>
      </div>
    </div>

    <!-- 底部固定双按钮容器 -->  
    <div v-if="state.detail.orderStatus == 0||state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" class="fixed-footer">  
      <div v-if="state.detail.orderStatus === 0"  class="price-wrapper">
        <p style="color: #ee0a24;font-size: 12px;margin-left: 30px;">
          ¥ 
          <span style="font-size:16px;color: #ee0a24;">{{ formatPricePartsSmart(state.detail.totalPrice).int }}</span>
          <span style="font-size:12px;color: #ee0a24;">{{ formatPricePartsSmart(state.detail.totalPrice).dec }}</span>
        </p>
      </div>
      <div class="button-container">
        <van-button v-if="state.detail.orderStatus == 0" round type="default" class="custom-btn-cancel" @click="handleCancelOrder(state.detail.orderNo)">取消订单</van-button>  
        <van-button v-if="state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" round type="default" class="custom-btn-cancel" @click="handleCancelOrder(state.detail.orderNo)">申请退款</van-button>  
        <van-button v-if="state.detail.orderStatus == 0" round type="primary" class="custom-btn" @click="handleSubmit(state.detail)">去支付</van-button>  
      </div>
    </div>  

    <!-- 二维码弹框 -->
    <van-popup
      v-model:show="showPopup"
      round
      position="center"
      :style="{ width: '80%', borderRadius: '12px' }"
    >
      <!-- 标题 -->
      <!-- <h3 class="popup-title">操作确认</h3> -->

      <!-- 内容区域 (整体居中) -->
      <div class="popup-content">
        <p v-if="state.detail.orderStatus == 0" style="margin-bottom: 20px;">是否取消该订单?</p>

        <p v-if="state.detail.orderStatus == 2||state.detail.orderStatus == 3||state.detail.orderStatus == 6" style="margin-bottom: 20px;">该订单剩余{{state.detail.fwbDetailVOS[0].remainCount}}份,是否<br>申请退款?</p>

        <!-- 居中带边框按钮 -->
        <van-button
          class="center-btn"
          type="default"
          @click="handleConfirm()"
        >
          是
        </van-button>
     

      <!-- 底部独立按钮 -->
      <van-button
        class="bottom-btn"
        block
        plain
        type="primary"
        @click="showPopup = false"
      >
        否
      </van-button>
    </div>
    </van-popup>
    <!-- 二维码弹框 -->
    <van-popup
      v-model:show="isPopupVisible"
      position="center"
      round
      :style="{ width: '65%', padding: '25px' }"
      @click-overlay="closeQRPopup"
    >
      <div class="qr-code-wrapper">
        <div ref="qrCanvas"></div>
      </div>
    </van-popup>

    <img
      v-if="isPopupVisible"
      src="@/assets/icon-close.png"
      class="close-icon-outside"
      @click="closeQRPopup"
      alt="关闭"
    />
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted, computed } from 'vue'
import sHeader from '@/components/SimpleHeader.vue'
import { getOrderDetail, cancelOrder, confirmOrder, payOrder,createOrder,getOrderPackDetail } from '@/service/order'
import { usePolling } from '../service/pollManager'
import { showConfirmDialog, showLoadingToast, closeToast, showSuccessToast, closeDialog, showToast } from 'vant'
import { useRouter,useRoute } from 'vue-router'
import QRCode from 'qrcodejs2-fix'
import { addCart } from '@/service/cart'  // 添加这行
const route = useRoute()
const router = useRouter()
const state = reactive({
  detail: {},
  showPay: false,
  orderId:''
})

const payStatusText = '待支付'
const countdownText = ref('')
let timer = null
let remainSeconds = 15 * 60 // 15分钟

function updateCountdown() {
  let remainSeconds = 15 * 60 // 默认15分钟
  if (state.detail.payDeadline) {
    const now = new Date()
    const end = new Date(state.detail.payDeadline.replace(/-/g, '/')) // 兼容iOS
    const diff = end - now
    remainSeconds = Math.ceil(diff / 1000) // 剩余秒数，向上取整
    if (remainSeconds < 0) remainSeconds = 0
  }
  const min = String(Math.floor(remainSeconds / 60)).padStart(2, '0')
  const sec = String(remainSeconds % 60).padStart(2, '0')
  countdownText.value = `<span style="color: #333333;">请在 </span><span style="color: #FA541C;">${min}:</span><span style="color: #FA541C;">${sec}</span><span style="color: #333333;"> 之内完成支付</span>`;
}

onMounted(() => {
  init()
  checkTextLength()
  updateCountdown()
  timer = setInterval(updateCountdown, 1000)
})
onUnmounted(() => {
  clearInterval(timer)
})

const getStatusIcon = computed(() => {
  const status = state.detail.orderStatus;
  if (status === 0) {
    return '/src/assets/icon-clock.png'; // 待支付
  } else if (status === -2) {
    return '/src/assets/icon-cancel.png'; // 待发货
  } else if (status === 2 || status === 3 || status === 6) {
    return '/src/assets/icon-unuse.png'; // 待使用
  } else if (status === 4 || status === 5) {
    return '/src/assets/icon-used.png'; // 已收货/完成
  } else if (status === -3 || status === -1 ) {
    return '/src/assets/icon-refound.png'; // 已取消/关闭
  }else if(state.detail.remainTime === 0){
    return '/src/assets/icon-over.png'; // 已过期
  }else if(state.detail.goodsServiceIsBlock === true){
    return '/src/assets/icon-stop.png'; // 已过期
  }
  return '/src/assets/icon-clock.png'; // 默认
});

const redirectToService = (url) => {
  if (url) window.location.href = url;
};

const formatPrice = (price) => {
  return Number.isInteger(price) ? 
    price :               // 整数直接显示（如：100）
    price.toFixed(2)      // 小数保留两位（如：99.90）
}

const goTo = (id) => {
  // console.log('1111111111')
  router.push({ path: `/order-detail-refund/${id}`})
  // router.replace({ path: `/order-detail/${id}`}).then(() => {
  //   router.replace({ path: `/order-detail/${id}` });
  // });
  // const { data } =  getOrderDetail(id)
  // state.detail = data
  // closeToast()

  // const newQuery = { 
  //   ...router.currentRoute.value.query,
  //   id: id
  // };
  // const fullPath = router.resolve({
  //   path: '/order-detail',
  //   query: newQuery
  // }).href;
  // window.open(fullPath, '_blank');

}

const formatPricePartsSmart = (price) => {
  const n = Number(price)
  if (Number.isInteger(n)) {
    return { int: n.toString(), dec: '' }
  } else {
    const [int, dec] = n.toFixed(2).split('.')
    return { int, dec: '.' + dec }
  }
}


const getColor = (score) => {

// item.orderStatus===0?'待支付':item.orderStatus===1?'待发货':item.orderStatus===2||item.orderStatus===3||item.orderStatus===6?'待使用':item.orderStatus===4||item.orderStatus===5||item.orderStatus===-1||item.orderStatus===-2?'已收货':item.orderStatus===-3?'已取消':'未知状态'

  if (score ===0) return '#FA541C' // 优秀绿色
  if (score ===1) return '#FF9500' // 及格橙色
  if (score ===2||score ===3||score ===6) return '#FF9500' // 及格橙色
  if (score ===4||score ===5||score ===-1||score ===-2||score ===-3) return '#999999' // 及格橙色
  return '#999999' // 不及格红色
}

const init = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true
  });
  const { id } = route.params
  const { data } = await getOrderPackDetail(id)
  state.detail = data
  closeToast()

  if(state.detail.orderStatus===2||state.detail.orderStatus===3||state.detail.orderStatus===6){
 // 确保 DOM 更新后生成二维码
 setTimeout(() => {
        // generateQRCode(state.detail.orderNo); // 替换为你的链接或文本
      }, 50);

  }
  
}

const isPopupVisible = ref(false);
const showPopup = ref(false);
const qrCanvas = ref(null);
let qrcodeInstance = null;

    // 生成二维码
    const generateQRCode = async (text) => {
      try {
        // console.log(this.qrcode,'qrcode')
        // if(this.qrcode !=null){
        //   this.qrcode.clear();
        // }
      
        if (qrcodeInstance) {
          qrCanvas.value.innerHTML = ''; // 清空容器内容 ‌:ml-citation{ref="1,4" data="citationList"}
    qrcodeInstance = null;
  }
  qrcodeInstance  =  new QRCode(qrCanvas.value,{
      text:text,
         width: 180,
       height: 180,
       
       colorDark: "#000000",
       colorLight: "#ffffff",
       correctLevel: QRCode.CorrectLevel.H
       });
        // qrcode.clear();
      //   new QRCode(qrCanvas.value, {
      //   text: "https://www.baidu.com/", // 需要转换为二维码的内容
      //   width: 100,
      //   height: 100,
      //   colorDark: "#000000",
      //   colorLight: "#ffffff",
      //   correctLevel: QRCode.CorrectLevel.H
      // });


      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    }
    // 打开弹框
    const showQRPopup  = (orderNo) => {
      isPopupVisible.value = true;
      // 确保 DOM 更新后生成二维码
      setTimeout(() => {
        generateQRCode(orderNo); // 替换为你的链接或文本
      }, 50);
      // usePollingx
    }


  // 关闭弹框
  const usePollingx  = () => {
          // 初始化轮询实例
          const { 
  data, 
  isPolling, 
  errorCount,
  start,
  stop
} = usePolling({
  url: '/api/real-time-data',
  interval: 8000,
  onSuccess: (data) => {
    console.log('最新数据:', data)
    isPopupVisible.value = false;
    state.status = 4;
    router.push({ path: '/order', query: { id } })
  },
  onError: (err) => {
    console.error('轮询错误:', err)
  }
})
    }



    // 关闭弹框
    const closeQRPopup  = () => {
      isPopupVisible.value = false;
      // router.push({ path: '/order/3'})
    }

    const handleConfirm  = () => {
      // showPopup.value = true;
      cancelOrder(state.orderId).then(res => {
        // console.log('111111')
      if (res.resultCode === 200) {
        showSuccessToast('取消成功')
        showPopup.value = false;
        init();
        // router.go(-1)
        // router.push({ path: '/order/5'})
        // init()
        // state.status = '3'
        // onRefresh()
        
        
       
      }else{
        showFailToast('取消失败')
        showPopup.value = false;
      }
    })
    };

const handleCancelOrder = (id) => {
  showPopup.value = true;
  state.orderId = id;
  // showConfirmDialog({
  //   title: '确认取消订单？',
  // }).then(() => {
  //   cancelOrder(id).then(res => {
  //     if (res.resultCode == 200) {
  //       showSuccessToast('删除成功')
  //       init()
  //     }
  //   })
  // }).catch(() => {
  //   // on cancel
  // });
}

const handleConfirmOrder = (id) => {
  showConfirmDialog({
    title: '是否确认订单？',
  }).then(() => {
    confirmOrder(id).then(res => {
      if (res.resultCode == 200) {
        showSuccessToast('确认成功')
        init()
      }
    })
  }).catch(() => {
    // on cancel
  });
}

const showPayFn = () => {
  state.showPay = true
}

const handlePayOrder = async (id, type) => {
  await payOrder({ orderNo: id, payType: type })
  state.showPay = false
  init()
}

const close = () => {
  closeDialog
}

const handleSubmit = async (item) => {
 try {
// 先加入购物车
//  const { data: cartData, resultCode } = await addCart({ 
//  goodsCount: item.orderItemVOs[0].goodsCount, 
//  goodsId: item.orderItemVOs[0].goodsId 
//  })

//  if(resultCode !== 200) {
// showToast('加入购物车失败')
// return
// }
//  console.log("handleSubmit",cartData)

 // 构造支付参数
 const wxAppid = window.localStorage.getItem('wxAppid')
 const wxOpenid = window.localStorage.getItem('wxOpenid')
const alipayUserId = window.localStorage.getItem('alipayUserId')
        // 根据环境构造remark2
        let remark2 = "";
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
            wxAppid &&
            wxOpenid
        ) {
            // 微信环境
            remark2 = JSON.stringify({
                openid: wxOpenid,
                appid: wxAppid,
            });
        } else if (navigator.userAgent.indexOf("AliApp") > -1 && alipayUserId) {
            //支付宝
            // showToast("未获取到用户信息");
            // 支付宝环境
            remark2 = JSON.stringify({
                openid: alipayUserId,
            });
        } else {
            showToast("未获取到用户wxAppid和wxOpenid");
            return;
        }

        console.log("remark2remark2remark2remark2", remark2);


 const baseUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname.split('/')[0]}/#/order-detail/${item.orderNo}`
 // 创建订单
const params = {
  orderNo: item.orderNo,
callBackUrl: baseUrl,
 remark2
}

const res = await payOrder(params)
 console.log("handleSubmit2",res) 
 if (res.resultCode === 200 && res.data?.cashierUrl) {
// 跳转到收银台
window.location.href = res.data.cashierUrl
 } else {
 showToast('拉起收银台失败')
 }
 } catch (error) {
 showToast('下单失败')
}
}

// 示例数据
const orderList = [
  {
    id: 1,
    img: 'https://img.yzcdn.cn/vant/cat.jpeg',
    name: '医生姓名',
    position:'副主任医师',
    desc: '科室名称',
    type: '图文咨询',
    count: 3
  },
  {
    id: 2,
    img: 'https://img.yzcdn.cn/vant/cat.jpeg',
    name: '医生姓名',
    desc: '科室名称',
    position:'副主任医师',
    type: '电话咨询',
    count: 1
  },
  {
    id: 3,
    img: 'https://img.yzcdn.cn/vant/cat.jpeg',
    name: '医生姓名',
    desc: '科室名称',
    position:'副主任医师',
    type: '在线复诊',
    count: 2
  },
  {
    id: 4,
    img: 'https://img.yzcdn.cn/vant/cat.jpeg',
    name: '标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题标题',
    desc: '规格：白色 XXXL 长款',
    type: '',
    count: 1
  }
]

// 添加计算属性处理 label 文本（放在 state 定义之后）
const getLabelText = computed(() => {
  const status = state.detail.orderStatus
  if (status === 0) {
    return countdownText.value // 待支付时显示倒计时
  } else if (status === 2 || status === 3 || status === 6) {
    // 计算剩余天数
    let remainingDays = 0
    if (state.detail.remainTime) {
      const now = new Date()
      const end = new Date(state.detail.remainTime.replace(/-/g, '/')) // 兼容iOS
      const diff = end - now
      remainingDays = Math.ceil(diff / (1000 * 60 * 60 * 24))
      if (remainingDays < 0) remainingDays = 0
    } else {
      remainingDays = 89 // 默认值
    }
    return `<span style="color: #333333;">有效期剩余</span><span style="color: #FA541C;">${remainingDays}</span><span style="color: #333333;">天，请即时使用</span>`
  } else if (status === 1) {
    return '商家正在处理中'
  } else if (status === 4 || status === 5) {
    return '服务已完成'
  } else if (status === -3) {
    return state.detail.payStatus === 3 ? '订单已关闭' : '订单已取消'
  }
  return '该订单已取消'
})

// 添加复制功能
const copyPayOrderNo = () => {
  if (state.detail.payOrderNo) {
    navigator.clipboard.writeText(state.detail.payOrderNo).then(() => {
      showToast('复制成功')
    }).catch(() => {
      showToast('复制失败')
    })
  }
}

const shouldWrap = ref(false);
const textStyle = ref({});
const nameText = ref(null);

// onMounted(() => {
//   init();
//   checkTextLength();
// });

function checkTextLength() {
  if (!nameText.value || !state.detail.name) return;
  
  // 降级方案：直接使用字符长度判断
  shouldWrap.value = state.detail.name.length > 13;
  textStyle.value = shouldWrap.value 
    ? { 
        'display': '-webkit-box',
        '-webkit-line-clamp': '2',
        '-webkit-box-orient': 'vertical'
      }
    : {};
}

</script>

<style lang="less" scoped>
.order-detail-box {
  background: #f8f8f8;
  min-height: 100vh;
}

.order-detail-bg {
  min-height: 100vh;
  background: transparent;
  padding: 0;
}
.order-detail-main {
  max-width: 420px;
  margin: 0 auto;
  padding: 0 0 80px 0;
}

/* 卡片样式，和 OrderServicepack.vue 一致 */
.card {
  background: #fff;
  border-radius: 8px;
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 5px 0px 5px 0px; /* 关键：左右16px */
  box-shadow: none;
}
.card:last-child {
  margin-bottom: 0;
}

/* 支付状态cell局部样式 */
.pay-status-cell {
  margin-bottom: 0;
  padding-left: 25px;
  padding-top: 15px;
  min-height: 120px;
  background: url("@/assets/icon-bg.png") no-repeat center center; // 使用 icon-bg 图片作为背景
  background-size: cover; // 确保背景图片覆盖整个区域
  border-radius: 0 !important; // 取消圆角
  :deep(.van-cell__label) {
    color: #999;
    font-size: 13px;
    margin-top: 2px;
  }
}

.order-list-title {
  font-weight: bold;
  font-size: 15px;
 margin-left: 15px;
 margin-top: 5px;
 margin-bottom: 10px;
}

.item-img-line {
    display: block;
    // margin: 0 auto;
    margin-left: 10px;
    margin-bottom: 10px;
    margin-top: -18px;
    max-width: 94.5%;
   
    height: auto;
    object-fit: contain;
  }

.order-list-item {
  background: #F8FDFC;
  border-radius: 8px;
  margin-left: 15px;
  margin-right: 15px;
  margin-bottom: 10px;
  :deep(.van-cell__title) {
    display: flex;
    align-items: center;
  }
  .item-left {
    display: flex;
    align-items: flex-start;
  }
  .item-img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 14px;
    object-fit: cover;
    background: #f5f5f5;
    flex-shrink: 0;
  }
 
  .item-info {
    flex: 1;
    min-width: 0;
  }
  .item-name-row {
    display: flex;
    align-items: center;
    min-width: 0;
  }
  .item-name {
    font-weight: bold;
    font-size: 14px;
    line-height: 1.7;
    white-space: nowrap;      // 默认不换行
    overflow: hidden;
    text-overflow: ellipsis;  // 超出时显示省略号
    // margin-right: 6px;
  }
  

  .position-tag {
    display: inline-block;
    margin-left: 0;
    color: #A77D2A;
    background: #FDF3E0;
    padding: 0 5px;
    border-radius: 4px;
    font-size: 12px;
    line-height: 20px;
    vertical-align: middle;
    white-space: nowrap;
    flex-shrink: 0;
  }
  .item-desc {
    font-size: 12px;
    color: #666;
  }
  .item-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    .item-type {
      color: #3EBFA0;
      font-size: 13px;
    }
    .item-count {
      color: #666;
      font-size: 13px;
      margin-top: -2px;
    }
  }
}

/* 非Webkit浏览器备用方案 */
@supports not (-webkit-line-clamp: 2) {
  .item-name.multi-line {
    max-height: 3.4em; /* 2行高度 */
    line-height: 1.7;
  }
}

.custom-value {
  text-align: right !important;
}

.fixed-footer {  
  position: fixed;  
  bottom: 0;  
  left: 0;  
  right: 0;  
  padding: 12px 16px;  
  background: #fff;  
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);  
  display: flex;  
  align-items: center;
  justify-content: space-between;
  z-index: 10;
}
.price-wrapper {
  flex: 0 0 auto;
  margin: 0;
  padding: 0;
}
.button-container {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
.custom-btn-cancel {
  height: 32px;
  width: 85px;
  border: 1px solid #ccc !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: 12px;
}
.custom-btn {
  height: 32px;
  width: 85px;
  background: #FF7430;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  font-size: 12px;
  border: none;
  outline: none;
  margin-left: 8px;
}

@supports (padding-bottom: env(safe-area-inset-bottom)) {  
  .fixed-footer {  
    padding-bottom: calc(12px + env(safe-area-inset-bottom));  
  }  
}

.qr-code-wrapper {
  text-align: center;
  // height: 300px;
  // width: 80%;
  display: grid;
  place-items: center;
}
.tip {
  margin-top: 50px;
  color: #666;
  border: none;
  outline: none;
}
.popup-content {
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.center-btn {
  width: 60%;
  border: 1px solid #01CDA7 !important;
  color: #01CDA7 !important;
  border-radius: 20px;
}
.bottom-btn {
  margin-top: 10px;
  color: #333333 !important;
  border-radius: 0 0 12px 12px;
  border: none;
  outline: none;
}
/* 移除所有对 van-cell 的 margin/padding 的自定义样式 */
:deep(.van-cell) {
  width: auto !important;
}

.custom-title {
  display: flex;
  align-items: center;
  gap: 6px; // 图片和文本间距
}

.title-icon {
  width: 16px; // 图片大小
  height: 16px;
}

:deep(.custom-cell) {
  min-height: 20px !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
:deep(.custom-cell .van-cell__value) {
  color: #333333 !important;
}

// 当字符数 > 13 时强制换行
.force-wrap {
  white-space: normal !important;
  word-break: break-word !important;
  text-overflow: clip !important; // 禁用省略号
}

.verify-btn {
  color: #01CDA7 !important;           /* 文字颜色 */
  background: #fff !important;         /* 背景色 */
  border: 1.5px solid #01CDA7 !important; /* 边框颜色 */
  font-size: 15px;
  font-weight: bold;
  margin: 16px auto;
  margin-top: 3px;
  // margin-top: -1px;
  border-radius: 24px !important;
  width: 50%;
  height: 40px;
  display: block;
}

.qr-code-popup-content {
  position: relative;
  padding-bottom: 40px; /* 给关闭按钮留空间 */
}



.close-icon-outside {
  position: fixed;
  left: 50%;
  top: 75%; /* 视实际弹窗高度调整，或用JS动态计算 */
  transform: translate(-50%, 0);
  width: 40px;
  height: 40px;
 
  border-radius: 50%;
  padding: 6px;
  box-sizing: content-box;
  cursor: pointer;
  z-index: 3001; /* 保证在弹窗之上 */
  
}
</style>
