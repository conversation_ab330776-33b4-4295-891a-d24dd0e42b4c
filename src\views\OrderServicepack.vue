<!--
 * 严肃声明：
 * 开源版本请务必保留此注释头信息，若删除我方将保留所有法律责任追究！
 * 本系统已申请软件著作权，受国家版权局知识产权以及国家计算机软件著作权保护！
 * 可正常分享和学习源码，不得用于违法犯罪活动，违者必究！
 * Copyright (c) 2020 陈尼克 all rights reserved.
 * 版权所有，侵权必究！
 *
-->

<template>
  <div class="order-box">
    <!-- <s-header :name="'商城订单'" :back="'/user'"></s-header> -->
    <van-tabs @click-tab="onChangeTab" :color="'#00bfae'" :title-active-color="'#00bfae'" title-inactive-color="#999999"  class="custom-tabs" v-model:active="state.status" :immediate-check="false">
      <van-tab 
        :title-style="{
          fontSize: state.status === '' ? '16px' : '14px',
          
        }" 
        title="全部" 
        name=""
      ></van-tab>
      <van-tab :title-style="state.status === '0' ? { fontSize: '16px' } : { fontSize: '14px' }"  title="待支付" name="0"></van-tab>
      <!-- <van-tab :title-style="state.status === '1' ? { fontSize: '16px' } : { fontSize: '14px' }" title="待发货" name="1"></van-tab> -->
      <van-tab :title-style="state.status === '2' ? { fontSize: '16px' } : { fontSize: '14px' }" title="待使用" name="2"></van-tab>
      <!-- <van-tab :title-style="state.status === '3' ? { fontSize: '16px' } : { fontSize: '14px' }" title="已发货" name="3"></van-tab> -->
      <van-tab :title-style="state.status === '3'? { fontSize: '16px' } : { fontSize: '14px' }" title="已完成" name="3"></van-tab>
      <!-- <van-tab :title-style="state.status === '5' ? { fontSize: '16px' } : { fontSize: '14px' }"  title="售后" name="5"></van-tab> -->
    
    </van-tabs>
    <!-- 历史服务包按钮 -->
    <div class="history-btn" @click="goToHistory">
      <img src="@/assets/icon-history.png" alt="历史服务包" class="history-icon" />
      <span class="history-text">历史服务包</span>
      <span class="arrow">›</span>
    </div>
    <div class="content">
      <van-pull-refresh v-model="state.refreshing" @refresh="onRefresh" class="order-list-refresh">
        <van-list
          v-model:loading="state.loading"
          :finished="state.finished"
          finished-text="没有更多了"
          @load="onLoad"
          @offset="10"
        >
          <div v-for="(item, index) in state.list" :key="index" class="order-item-box">
            <!-- <div class="order-item-header">
              <span>订单时间：{{ item.createTime }}</span>
              <span>{{ item.orderStatusString }}</span>
            </div> -->
            <div @click.stop="goTo(item.orderNo)" class="custom-box">
            <van-card
             
              :key="item.goodsId"
              
             

             
              :title="item.goodsName"
              
            
              
               
            >
             <!-- 使用 price 插槽自定义内容 -->
    <!-- <template #price>
      <p style="color: #333333;font-size: 12px;">
        ¥ <span style="font-size:16px;color: #333;">{{ formatPricePartsSmart(one.sellingPrice).int }}</span>
        <span style="font-size:12px;color: #333;">{{ formatPricePartsSmart(one.sellingPrice).dec }}</span>
      </p>
    </template> -->
           <!-- 右上角自定义文字 -->
      <template #tags>
        <div
       
        :style="{ color: getColor(item.orderStatus) }"
   
        class="corner-tag">{{
      
      item.orderStatus === 0 ? '待支付' :
      item.orderStatus === -2 ? '已取消' :
      item.remainTime === 0 ? '已过期' :
      item.goodsServiceIsBlock === true ? '已停用' :
        (item.orderStatus === 2 || item.orderStatus === 3 || item.orderStatus === 6) ? '待使用' :
        (item.orderStatus === 4 || item.orderStatus === 5) ? '已使用' :
        (item.orderStatus === -3 && item.payStatus === -1) ? '退款成功' : ''
      }}

        </div>
      </template>
            </van-card>

       <!-- 新增：治疗项目展示 -->
<div   class="treatment-list" v-if="item.fwbDetailVOS && item.fwbDetailVOS.length">
  <div class="treatment-items-container">
    <div
      class="treatment-item"
      v-for="(treat, idx) in item.fwbDetailVOS.filter(t => !t.serviceIsGift)"
      :key="idx"
    >
      <img src="@/assets/treat.png" class="treat-icon" alt="" />
      <span class="treat-name">{{ treat.serviceName }}</span>
    </div>
  </div>
  <div class="gift-row" v-if="item.fwbDetailVOS.some(t => t.serviceIsGift)">
    <span class="gift-label">赠送：</span>
    <span
      v-for="(treat, idx) in item.fwbDetailVOS.filter(t => t.serviceIsGift)"
      :key="idx"
      class="gift-value"
    >{{ treat.serviceName }}</span>
  </div>
</div>



            <!-- <van-cell v-if="state.list.length > 0 && (state.status == 0||state.status == 2)"
      v-for="item in state.list" 
      :key="item.id"
      :title="item.text"
    /> -->
    <!-- 实线 -->
    <!-- <van-divider v-if="state.list.length > 0 && (item.orderStatus == 0||item.orderStatus == 2||item.orderStatus == 3||item.orderStatus == 6)" :style="{ width: '90%', margin: '0 auto'}" /> -->

  


  <div v-if="state.list.length > 0 && item.orderStatus == 0" class="button-group">
    <div class="price-wrapper">
      <van-cell 
        
        value-class="custom-value" 
        :border="false"
      >
        <p style="color:  #ee0a24;font-size: 12px;">
          ¥ 
          <span style="font-size:16px;color: #ee0a24;">{{ formatPricePartsSmart(item.totalPrice).int }}</span>
          <span style="font-size:12px;color: #ee0a24;">{{ formatPricePartsSmart(item.totalPrice).dec }}</span>
        </p>
      </van-cell>
    </div>
    <div class="button-container">
      <van-button 
        class="custom-btn-cancel"
        type="default"
        @click.stop="handleCancelOrder(item.orderNo)"
      >
        取消订单
      </van-button>
      <van-button 
        type="primary"
        round
        class="custom-btn" 
        @click.stop="handleSubmit(item)"
      >
        去支付
      </van-button>
    </div>
  </div>

            <!-- 触发按钮 - 申请退款 -->
            <div v-if="state.list.length > 0 && (item.orderStatus == 2||item.orderStatus == 3||item.orderStatus == 6)" class="button-group">
              <div class="button-container">
                <van-button 
                  class="custom-btn-cancel"
                  type="default"
                  @click.stop="handleCancelOrder(item.orderNo)"
                >
                  申请退款
                </van-button>
              </div>
            </div>
  
  </div>
    

    

          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <van-popup
  v-model:show="showPopup"
  round
  position="center"
  :style="{ width: '80%', borderRadius: '12px' }"
>
  <!-- 标题 -->
  <!-- <h3 class="popup-title">操作确认</h3> -->

  <!-- 内容区域 (整体居中) -->
  <div class="popup-content">
    <p style="margin-bottom: 20px;">是否取消该订单？</p>

    <!-- 居中带边框按钮 -->
    <van-button
      class="center-btn"
      type="default"
      @click="handleConfirm"
    >
      是
    </van-button>
  

  <!-- 底部独立按钮 -->
  <van-button
    class="bottom-btn"
    block
    plain
    type="primary"
    @click="showPopup = false"
  >
    否
  </van-button>
</div>
</van-popup>


    <!-- 二维码弹框 -->
    <van-popup
      v-model:show="isPopupVisible"
      position="center"
      round
      :style="{ width: '80%', padding: '20px' }"
      @click-overlay="closeQRPopup"
    >
      <div class="qr-code-wrapper" >
        <div style="margin-top: 20px;" ref="qrCanvas"></div>
        <p class="tip">请将核销二维码出示给工作人员</p>
        <van-button plain  @click="closeQRPopup">关闭</van-button>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import {  ref,reactive,onMounted,nextTick,getCurrentInstance   } from 'vue';
import sHeader from '@/components/SimpleHeader.vue'
import { userAuthorize} from '@/service/api'
import { getOrderList,cancelOrder,createOrder, payOrder,getOrderPackList  } from '@/service/order'
import { usePolling } from '../service/pollManager'
import { useRouter,useRoute } from 'vue-router'
import QRCode from 'qrcodejs2-fix'
// import QRCode from 'qrcodejs2'
import { showConfirmDialog, showLoadingToast, closeToast, showSuccessToast, closeDialog, showFailToast } from 'vant'
import { refresh } from 'less';


import { addCart } from '@/service/cart'  // 添加这行

const router = useRouter()
const route = useRoute()
const state = reactive({
  status: '',
  loading: false,
  finished: false,
  refreshing: false,
  list: [],
  page: 1,
  orderId:'',
  totalPage: 0
})

const treatments = [
  {name: '纳晶微针治疗*1' },
  { name: '低能量激光光治疗*1' },
  {  name: '中药头皮蒸汽治疗*1' }
]

const formatPrice = (price) => {
  return Number.isInteger(price) ? 
    price :               // 整数直接显示（如：100）
    price.toFixed(2)      // 小数保留两位（如：99.90）
}

const formatPricePartsSmart = (price) => {
  const n = Number(price)
  if (Number.isInteger(n)) {
    return { int: n.toString(), dec: '' }
  } else {
    const [int, dec] = n.toFixed(2).split('.')
    return { int, dec: '.' + dec }
  }
}

const getColor = (score) => {

// item.orderStatus===0?'待支付':item.orderStatus===1?'待发货':item.orderStatus===2||item.orderStatus===3||item.orderStatus===6?'待使用':item.orderStatus===4||item.orderStatus===5?'已收货':item.orderStatus===-3?'已取消':'未知状态'

  if (score ===0) return '#FA541C' // 优秀绿色
  if (score ===1) return '#FF9500' // 及格橙色
  if (score ===2||score ===3||score ===6) return '#FF9500' // 及格橙色
  if (score ===4||score ===5||score ===-1||score ===-2||score ===-3) return '#999999' // 及格橙色
  return '#999999' // 不及格红色
}

// Add polling for order status
// usePolling(() => {
//   if (state.status === '0') { // Only poll for pending payment orders
//     loadData()
//   }
// }, 5000) // Poll every 5 seconds

const isPopupVisible = ref(false);
const showPopup = ref(false);
const qrCanvas = ref(null);
let qrcodeInstance = null;
const instance = getCurrentInstance();
// const vm = this;
const proxy = instance.proxy; 
    // 生成二维码
    const generateQRCode = async (text) => {
      try {
        // console.log(this.qrcode,'qrcode')
        // if(this.qrcode !=null){
        //   this.qrcode.clear();
        // }
       
        if (qrcodeInstance) {
          qrCanvas.value.innerHTML = ''; // 清空容器内容 ‌:ml-citation{ref="1,4" data="citationList"}
    qrcodeInstance = null;
  }
  proxy.$nextTick(() => {

    qrcodeInstance  =  new QRCode(qrCanvas.value,{
      text:text,
         width: 150,
       height: 150,
       
       colorDark: "#000000",
       colorLight: "#ffffff",
       correctLevel: QRCode.CorrectLevel.H
       });

});

// nextTick(() => {  // 
//     // 更新 DOM
//     qrcodeInstance  =  new QRCode(qrCanvas.value, text,{
         
//          width: 150,
//        height: 150,
       
//        colorDark: "#000000",
//        colorLight: "#ffffff",
//        correctLevel: QRCode.CorrectLevel.H
//        });
//   });

        // qrcode.clear();
      //   new QRCode(qrCanvas.value, {
      //   text: "https://www.baidu.com/", // 需要转换为二维码的内容
      //   width: 100,
      //   height: 100,
      //   colorDark: "#000000",
      //   colorLight: "#ffffff",
      //   correctLevel: QRCode.CorrectLevel.H
      // });


      } catch (error) {
        console.error('生成二维码失败:', error);
      }
    };
   

    onMounted(() => {
     
      const { status } = route.params
      // console.log(status, 'sstatus');
      if(status==='4'){
        state.status = ''
      }else{
        state.status = status
      }
      
      const savedTab = localStorage.getItem('lastActiveTab');
      if (savedTab) state.status = savedTab;
      
      // console.log(state.status, 'state.status');
})


    // 打开弹框
    const showQRPopup  = (orderNo) => {
      isPopupVisible.value = true;
      // 确保 DOM 更新后生成二维码
      
      setTimeout(() => {
        generateQRCode(orderNo); // 替换为你的链接或文本
      }, 50);
      // new QRCode(qrCanvas.value, 'https://www.baidu.com/', {
      //     width: 200, // 二维码大小
      //     margin: 2,  // 边距
      //     color: {
      //       dark: '#000000', // 二维码颜色
      //       light: '#ffffff' // 背景色
      //     }
      //   });
      // usePollingx
 


    };


 // 关闭弹框
 const usePollingx  = () => {
          // 初始化轮询实例
          const { 
  data, 
  isPolling, 
  errorCount,
  start,
  stop
} =      usePolling({
  url: '/api/real-time-data',
  interval: 8000,
  onSuccess: (data) => {
    console.log('最新数据:', data)
    isPopupVisible.value = false;
    state.status = 4;
  },
  onError: (err) => {
    console.error('轮询错误:', err)
  }
})
    }

    // 关闭弹框
    const closeQRPopup  = () => {
      isPopupVisible.value = false;
      // state.status = '3'
      // onRefresh()
    };

    const handleConfirm  = () => {
      // showPopup.value = true;
      // state.status = '2'
      cancelOrder(state.orderId).then(res => {
        // console.log('111111')
      if (res.resultCode === 200) {
        showSuccessToast('取消成功')
        showPopup.value = false;
        // init()
        // state.status = '5'
        onRefresh()
      //   setTimeout(() => {
      //     state.status = 2
      // }, 1000);
        
        
       
      }else{
        showFailToast('取消失败')
        showPopup.value = false;
      }
    })
    };


const loadData = async () => {
  const sss = reactive({
  ss: ''
})
  if(state.status==='2'){
    sss.ss = '2,3,6'
  }else if(state.status==='3'){
    sss.ss = '4,5,-1,-2'
  }else if(state.status==='0'){
    sss.ss = '0'
  }else if(state.status==='1'){
    sss.ss = '1'
  }else if(state.status==='5'){
    sss.ss = '-3'
  }
  const { data, data: { list } } = await getOrderPackList({ pageNumber: state.page, status: sss.ss,goodsServiceIsBlock:'' })
  state.list = state.list.concat(list)
  // console.log(state.list,'state.list');
  state.totalPage = data.totalPage
  state.loading = false;
  if (state.page >= data.totalPage) state.finished = true
}

const onChangeTab = ({ name }) => {
  // 这里 Tab 最好采用点击事件，@click，如果用 @change 事件，会默认进来执行一次。
  state.status = name
  onRefresh()
  localStorage.setItem('lastActiveTab', name);
  // console.log(state.status,'state.status');
}

const goTo = (id, event) => {
  if (event) event.stopPropagation();
  router.push({ path: `/order-detail-pack/${id}`})
}

const goBack = () => {
  router.go(-1)
}

const onLoad = () => {
  if (!state.refreshing && state.page < state.totalPage) {
    console.log(state.page)
    console.log(state.totalPage)
    state.page = state.page + 1
  }
  if (state.refreshing) {
    state.list = [];
    state.refreshing = false;
  }
  loadData()
}

const onRefresh = () => {
  state.refreshing = true
  state.finished = false
  state.loading = true
  state.page = 1
  onLoad()
}

const handleCancelOrder = (id) => {
 
  showPopup.value = true;
  state.orderId =  id;
}


const handleSubmit = async (item) => {
 try {
// 先加入购物车
//  const { data: cartData, resultCode } = await addCart({ 
//  goodsCount: item.goodsCount, 
//  goodsId: item.goodsId 
//  })

//  if(resultCode !== 200) {
// showToast('加入购物车失败')
// return
// }
//  console.log("handleSubmit",cartData)

 // 构造支付参数
 const wxAppid = window.localStorage.getItem('wxAppid')
 const wxOpenid = window.localStorage.getItem('wxOpenid')
const alipayUserId = window.localStorage.getItem('alipayUserId')
// 根据环境构造remark2
        let remark2 = "";
        if (
            navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 &&
            wxAppid &&
            wxOpenid
        ) {
            // 微信环境
            remark2 = JSON.stringify({
                openid: wxOpenid,
                appid: wxAppid,
            });
        } else if (navigator.userAgent.indexOf("AliApp") > -1 && alipayUserId) {
            //支付宝
            // showToast("未获取到用户信息");
            // 支付宝环境
            remark2 = JSON.stringify({
                openid: alipayUserId,
            });
        } else {
            showToast("未获取到用户wxAppid和wxOpenid");
            return;
        }

        console.log("remark2remark2remark2remark2", remark2);


 const baseUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname.split('/')[0]}/#/order-detail/${item.orderNo}`
 // 创建订单
const params = {
  orderNo: item.orderNo,
callBackUrl: baseUrl,
 remark2
}

const res = await payOrder(params)
 console.log("handleSubmit2",res) 
 if (res.resultCode === 200 && res.data?.cashierUrl) {
// 跳转到收银台
window.location.href = res.data.cashierUrl
 } else {
 showToast('拉起收银台失败')
 }
 } catch (error) {
 showToast('下单失败')
}
}

const goToHistory = async () => {
  // 假设 jumpUrl 来源于某个 item
  // let jumpUrl = "https://t-zbzk.zjwlyy.cn/h5/patient/myOrders/list?projectId=1g64mbep41moiorf1vksgpe1k72mjf46&orgId=1hflbdrheoj89gt2bvtplc10pn0v93h2&appId=1hflmfdpr2orh6gjcq5is92bfokaa2sp"; // 替换为实际 jumpUrl
  // VUE_APP_ServiceBagClientId = 'DFE1FCD95AD04478'
  // console.log("userId", window.localStorage.getItem("userId"));
  // console.log("userId", new Date().getTime());
  // VUE_APP_ServiceBagUrl = 'https://smart04.gjwlyy.com'
// VUE_APP_ServiceBagClientId = '8D33D1E185FD47B6'
  
let jumpUrl = "https://smart04.gjwlyy.com/h5/patient/myOrders/list?projectId=1g64mbep41moiorf1vksgpe1k72mjf46&orgId=1hflbdrheoj89gt2bvtplc10pn0v93h2&appId=1hflmfdpr2orh6gjcq5is92bfokaa2sp"; // 替换为实际 jumpUrl


  const res = await userAuthorize({
    userId:window.localStorage.getItem("userId"),
    clientId :'8D33D1E185FD47B6',
    reqSeq:new Date().getTime()
  });
  console.log("auth授权", res);

  let openId;
  if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1) {
    openId = window.localStorage.getItem("wxOpenid");
  } else {
    openId = window.localStorage.getItem("alipayUserId");
  }

  jumpUrl =
    jumpUrl.indexOf("?") > -1
      ? `${jumpUrl}&code=${res.code}&openId=${openId}&time=${new Date().getTime()}`
      : `${jumpUrl}?code=${res.code}&openId=${openId}&time=${new Date().getTime()}`;
  console.log("code链接", jumpUrl);

  // 跳转
  window.location.href = jumpUrl;
};

</script>

<style lang="less" scoped>
.history-btn {
  width: 100%;
  background: #fff;
  color: #222;
  font-size: 15px;
  padding: 8px 16px;
  border-bottom: 1px solid #f2f2f2;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  margin-top: 10px;
  gap: 8px; /* 图片和文本之间的间距 */
}

.history-icon {
  width: 35px;
  height: 35px;
  margin-right: 0; /* 移除右侧间距 */
}

.history-text {
  flex: 1; /* 让文本占据剩余空间 */
  text-align: left; /* 确保文本左对齐 */
}

.history-btn .arrow {
  color: #999;
  font-size: 18px;
  margin-right: 25px
}

@import '../common/style/mixin';
.order-box {
  background: #f8f8f8;
  height:100vh;
  .order-header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10000;
    .fj();
    .wh(100%, 44px);
    line-height: 44px;
    padding: 0 10px;
    .boxSizing();
    color: #252525;
    background: #f8f8f8;
    border-bottom: 1px solid #dcdcdc;
    .order-name {
      font-size: 14px;
    }
  }
 /* 内容区域 */
.popup-content {
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 居中带边框按钮 */
.center-btn {
 
  width: 60%;
  border: 1px solid #01CDA7 !important;
  color: #01CDA7 !important;
  border-radius: 20px;
}

/* 底部按钮 */
.bottom-btn {
  margin-top: 10px;
  color: #333333 !important;
  // border-top: 1px solid #f0f0f0; /* 按钮上边框 */
  border-radius: 0 0 12px 12px; /* 匹配弹窗圆角 */
}
    .qr-code-wrapper {
          text-align: center;
          height: 300px;        /* 必须定义高度 */  
          display: grid;  
          place-items: center;  /* 同时水平和垂直居中 */  
        }




        .tip {
          // margin: 0px 0;
          margin-top: 50px;
          color: #666;
        }

    .custom-tabs {
      // position: fixed;
  //     left: 0;
  //     z-index: 1000;
  //     width: 100%;
  //     border-bottom: 1px solid #e9e9e9;
  //     flex: none !important;  /* 禁止 flex 压缩 */
  // min-width: 100px;      /* 设置最小宽度 */
  // white-space: nowrap;   /* 禁止换行 */
  // :deep(.van-tabs__nav) {
  //   justify-content: flex-start; // 确保整体左对齐
  // }
  // :deep(.van-tab) {
  //   flex: none !important; // 禁止标签自动拉伸
  //   // padding: 0 16px !important; // 调整标签内边距
  //   text-align: left !important; // 强制文字左对齐
  // }
  :deep(.van-tab:first-child) {
    flex: none !important;
    padding-right: 20px !important;
    padding-left: 20px !important; // 第一个标签左内边距
    margin-right: auto !important; // 将第一个标签推到最左侧
  }
    }
  



    .skeleton {
      margin-top: 60px;
    }
    .content {
      height: calc(~"(100vh - 70px)");
      overflow: hidden;
      overflow-y: scroll; 
      
      // margin-top: 34px;
    }
    .order-list-refresh {
      .van-card__content {
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .van-pull-refresh__head {
        background: #f9f9f9;
        // height: 1px;
      }
//       :deep(.van-pull-refresh__head) {
//   height: 10px !important; /* 自定义高度 */
// }
/* 在你的样式文件中 */
// .van-card__title {
//   color: #f00; /* 红色 */
//   font-size: 16px; /* 字体大小 */
// }
      .order-item-box {
        margin: 10px 10px;
        // background-color: #fff;
        .order-item-header {
          padding: 10px 20px 0 20px;
          display: flex;
          justify-content: space-between;
        }
        .van-card {
          background-color: transparent !important;  /* 核心代码 */  
  border: none !important;                   /* 清除边框干扰 */  
  box-shadow: none !important;               /* 移除阴影覆盖 */
        }
      /* 在你的样式文件中 */
      :deep(.van-card__title) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        width: 65%;
}
:deep(.van-card__desc) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        // width: 65%;
       margin-top: 40px;

}
:deep(.van-card__price) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        // width: 65%;
        display: flex !important;
        font-size: 10px;
        margin-top: -70px;
          justify-content: flex-end !important; 

}
:deep(.van-card__num) {
  // color: rgb(119, 0, 255); /* 红色 */
  // font-size: 16px; /* 字体大小 */
        // width: 65%;
       margin-top: 25px;

}
:deep(.van-cell) {
  height: auto !important;    /* Changed from fixed height */
  padding: 0;                /* Remove padding */
  line-height: normal;       /* Reset line height */
  background: transparent;   /* Make background transparent */
}
:deep(.van-cell__value > span) {
  color: #333 !important;
}

.corner-tag {
  position: absolute;
  top: -3px;
  right: -5px;
  // bottom: 5px;
  padding: 1px 1px;
  // background: #ee0a24;
  color: #ee0a24;
  // border-radius: 22px;
  font-size: 14px;
  z-index: 1;
  // box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
        .button-group {
          display: flex;
          align-items: center;
          width: 100%;
          margin-top: 8px;
          margin-bottom: 5px;
          box-sizing: border-box;
          justify-content: flex-end;
}

.price-wrapper {
  flex: 0 0 auto;
  margin-left: 15px;
  margin-top: 10px;
  padding-left: 0;
}

.button-container {
  display: flex;
  gap: 8px;
  margin-left: auto;
  padding-right: 10px;
}

/* Update van-cell styles */
:deep(.van-cell) {
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  line-height: normal;
  background: transparent;
  width: auto !important;
}

:deep(.van-cell__value) {
  padding: 0 !important;
  margin: 0 !important;
  flex: 0 0 auto !important;
}

:deep(.van-cell__value-wrapper) {
  padding: 0 !important;
  margin: 0 !important;
}

/* 覆盖 Vant 按钮默认块级样式 */
.button-group .van-button {
  display: inline-flex;
  border-radius: 20px !important;     /* 设置圆角半径 ‌:ml-citation{ref="5,7" data="citationList"} */
    /* 自定义边框 ‌:ml-citation{ref="4,5" data="citationList"} */
}
.custom-btn-cancel {
  // border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 85px;
  border: 1px solid #ccc !important;
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
}
.custom-btn {
  // border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 85px;
  background: linear-gradient(to right, #FF7430, #FF9D2E);
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
  margin-left: 8px;
}
.custom-btn-qr {
  // border-radius: 20px;
  // border: 0px solid #FF7430 !important;
  height: 32px;
  width: 120px;
  
  background-image: linear-gradient(to right, #FFA943, #FFC748) !important;
  display: flex !important;            /* 启用 Flex 布局 */
  justify-content: center !important;  /* 水平居中 */
  align-items: center !important;      /* 垂直居中 */
  font-size: 12px;
  margin-left: 8px;
}
.custom-box {
  display: flex;
  flex-direction: column; /* 垂直排列 */
  // align-items: center;    /* 水平居中 */
  justify-content: center; /* 垂直居中 */
  gap: 5px;
  // min-height: 100px;       /* 最小高度 */
  border-radius: 8px;      /* 圆角 */
  padding-top: 1px;
  padding-bottom: 5px;
  background: #ffffff;      /* 最小高度确保容器可见 */
}



/* 弹窗基础样式 */
.van-popup--center {
  background: #fff !important;
  // padding-bottom: 50px; /* 给底部按钮留空间 */
}

/* 标题样式 */
.popup-title {
  padding: 16px;
  margin: 0;
  font-size: 18px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0; /* 标题下边框 */
}

.history-btn {
  width: 100%;
  background: #fff;
  color: #222;
  font-size: 15px;
  padding: 14px 16px;
  border-bottom: 1px solid #f2f2f2;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
}
.history-btn .arrow {
  color: #999;
  font-size: 18px;
}

.treatment-list {
  display: flex;
  flex-direction: column; /* 垂直布局 */
  margin-left: 15px;
  margin-right: 15px;
  padding: 10px;
  background: #F8FDFC;
  border-radius: 8px;
  margin-top: -5px;
}

.treatment-items-container {
  display: flex;
  flex-wrap: wrap; /* 允许内容换行 */
  gap: 8px; /* 项目之间的间距 */
}

.treatment-item {
  display: flex;
  align-items: center;
  white-space: nowrap; /* 防止单个项目内换行 */
}

.treat-icon {
  width: 15px;
  height: 15px;
  margin-right: 4px;
}

.treat-name {
  color: #666666;
  font-size: 12px;
}

.gift-row {
  display: flex;
  align-items: center;
  color: #00bfae;
  font-size: 12px;
  margin-top: 8px; /* 与上方内容的间距 */
}

.gift-label {
  font-weight: bold;
}

.gift-value {
  word-break: break-all;
  margin-right: 8px;
}

:deep(.van-card__content) {
  padding: 1px 0 !important;      // 减少上下内边距
  min-height: unset !important;   // 取消最小高度
  line-height: 1.3 !important;    // 紧凑行高
}
:deep(.van-card__header) {
  margin-bottom: 2px !important;  // 标题和内容间距减小
}
:deep(.van-card__title) {
  font-size: 14px !important;
  line-height: 1.2 !important;
  margin-bottom: 0 !important;
}
:deep(.van-card__desc) {
  margin-top: 2px !important;
  font-size: 13px !important;
  line-height: 1.2 !important;
}

      }
    }
  }
</style>
