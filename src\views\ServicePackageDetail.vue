<!--
 * 服务包详情页面 - 从服务包列表进入，需要选择医生
-->

<template>
  <div class="service-package-detail">
    
    <div class="detail-content">
      <!-- 顶部图片横幅 -->
      <div class="banner-image">
        <img src="../../static-files/插图.png" alt="服务包主图" />
      </div>

      <!-- 服务包基本信息 -->
      <div class="package-info">
        <div class="package-name">{{ state.packageDetail.goodsName || '服务包名称服务包名称服务包名称' }}</div>
        <div class="package-sales">已售销量 {{ state.packageDetail.totalSales || '2000+' }}</div>
      </div>

      <!-- 主要内容区域 -->
        <div class="main-content">
          <div class="content-card">
            <div class="section-title">
              <span>服务内容</span>
            </div>
            <div class="service-list">
              <div
                v-for="(item, index) in state.packageDetail.serviceItems"
                :key="index"
                class="service-item"
              >
                <span class="service-name">{{ item.name }}</span>
                <span class="service-count">*{{ item.count }}</span>
              </div>
            </div>
          </div>
          <!-- 赠送内容 -->
          <div class="content-card" v-if="state.packageDetail.giftItems && state.packageDetail.giftItems.length > 0">
            <div class="section-title">
              <span>赠送内容</span>
            </div>
            <div class="gift-list">
              <div
                v-for="(item, index) in state.packageDetail.giftItems"
                :key="index"
                class="gift-item"
              >
                <span class="gift-name">{{ item.name }}</span>
                <span class="gift-count">*{{ item.count }}</span>
              </div>
            </div>
          </div>
          <div class="content-card" v-if="state.packageDetail.goodsDetailContent">
            <div class="section-title">
              <span>服务描述</span>
            </div>
            <div class="service-description" v-html="state.packageDetail.goodsDetailContent"></div>
          </div>
        </div>
    </div>

    <!-- 固定的有效期栏 -->
    <div class="validity-bar">
      <span class="period-label">使用期：</span>
      <span class="period-value">{{ state.packageDetail.validityPeriod || '永久使用' }}</span>
    </div>

    <!-- 底部价格和购买按钮 -->
    <div class="bottom-bar">
      <div class="total-price">
        总计
        <span class="price">{{state.packageDetail.price}}</span>
      </div>
      <van-button
        type="danger"
        class="buy-button"
        @click="handleBuy"
      >
        立即购买
      </van-button>
    </div>
    <!-- 选择医生弹窗 -->
    <van-popup
      v-model:show="state.showDoctorPopup"
      @close="handleCancelDoctorSelection"
      position="bottom"
      round
      closeable
      :style="{ maxHeight: '70%' }"
    >
      <div class="doctor-selection">
        <div class="popup-title">请选择服务医生</div>
        <div class="doctor-list">
          <div 
            v-for="doctor in state.doctorList" 
            :key="doctor.doctorId"
            class="doctor-item"
            @click="selectDoctor(doctor)"
          >
            <div class="doctor-avatar">
              <img :src="doctor.avatar || '/default-avatar.png'" alt="">
            </div>
            <div class="doctor-info">
              <div class="name-title-row">
                <div class="doctor-name">{{ doctor.doctorName }}</div>
                <div class="doctor-tag" >{{ doctor.staffTechnicalTitleName }}</div>
              </div>
              <div class="doctor-department">{{ doctor.deptName }}</div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getServicePackageDetail } from '@/service/good'
import { addCart } from '@/service/cart'
import { createOrder } from '@/service/order'
import sHeader from '@/components/SimpleHeader.vue'
import { showToast } from 'vant'


const route = useRoute()
const router = useRouter()

const state = reactive({
  packageDetail: {
    name: '',
    totalSales: '',
    serviceItems: [
      { name: '纳晶微针治疗', count: 1 },
      { name: '低能量激光治疗', count: 1 },
      { name: '中药头皮蒸汽浴治疗', count: 1 }
    ],
    giftItems: [
      { name: '项目名称项目名称项目名称', count: 1 }
    ],
    description: '',
    validityPeriod: '',
    price: 300,
    id: ''
  },
  doctorList: [
    {
      id: 1,
      name: '医生姓名',
      title: '主任医师',
      department: '科室名称',
      avatar: '',
      isMainDoctor: true
    },
    {
      id: 2,
      name: '医生姓名',
      title: '主任医师',
      department: '科室名称',
      avatar: '',
      isMainDoctor: true
    },
    {
      id: 3,
      name: '医生姓名',
      title: '主任医师',
      department: '科室名称',
      avatar: '',
      isMainDoctor: true
    }
  ],
  showDoctorPopup: false,
  selectedDoctor: null,
  unicode:null,
  selectedDeptId:null,
  quantity: 1
})



// 选择医生
// 添加取消选择医生的处理函数
const handleCancelDoctorSelection = () => {
  state.showDoctorPopup = false;
  state.selectedDoctor = null; // 清除已选择的医生数据
  state.selectedDeptId = null;
}

// 修改selectDoctor函数，直接调用handleBuy而非跳转
const selectDoctor = (doctor) => {
  state.selectedDoctor = doctor.doctorId
  state.selectedDeptId = doctor.deptId
  state.showDoctorPopup = false
  handleBuy() // 直接调用购买处理函数
}


const handleBuy = async () => {
  if (state.selectedDoctor) {
    try {
      // 先加入购物车
      const cartParams = {
        goodsCount: state.quantity,
        goodsId: state.packageDetail.id,
        doctorId: state.selectedDoctor,
        deptId: state.selectedDeptId,
        unicode:state.unicode,
        goodsSpecification: state.packageDetail.goodsSpecification
      };

      const { data: cartData, resultCode } = await addCart(cartParams);
      const baseUrl = location.origin + location.pathname + "#/order-detail-pack";
      console.log("baseUrlbaseUrlbaseUrlbaseUrl", baseUrl);
      if (resultCode !== 200) {
        showToast("加入购物车失败");
        return;
      }

      console.log("remark2remark2remark2remark2");
      // 构造支付参数
      const wxAppid = window.localStorage.getItem("wxAppid");
      const wxOpenid = window.localStorage.getItem("wxOpenid");
      const alipayUserId = window.localStorage.getItem("alipayUserId");
      let remark2 = "";

      if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 && wxAppid && wxOpenid) {
        remark2 = JSON.stringify({
          openid: wxOpenid,
          appid: wxAppid
        });
      } else if (navigator.userAgent.indexOf("AliApp") > -1 && alipayUserId) {
        remark2 = JSON.stringify({
          openid: alipayUserId
        });
      } else {
        showToast("未获取到用户信息");
        return;
      }

      console.log("remark2remark2remark2remark2", remark2);

      // 创建订单
      const params = {
        cartItemIds: [Number(cartData)],
        callBackUrl: baseUrl,
        remark2,
        doctorId: route.query.doctorId
      };

      const res = await createOrder(params);
      if (res.resultCode === 200 && res.data?.cashierUrl) {
        window.location.href = res.data.cashierUrl;
      } else {
        showToast("拉起收银台失败");
      }
    } catch (error) {
      showToast("下单失败");
    }
  } else {
    if(state.doctorList.length === 0){
        try {
            // 先加入购物车
            const cartParams = {
              goodsCount: state.quantity,
              goodsId: state.packageDetail.id,
              deptId: state.selectedDeptId,
              unicode:state.unicode,
              goodsSpecification: state.packageDetail.goodsSpecification
            };
          
            const { data: cartData, resultCode } = await addCart(cartParams);
            const baseUrl = location.origin + location.pathname + "#/order-detail-pack";
            console.log("baseUrlbaseUrlbaseUrlbaseUrl", baseUrl);
            if (resultCode !== 200) {
              showToast("加入购物车失败");
              return;
            }
          
            console.log("remark2remark2remark2remark2");
            // 构造支付参数
            const wxAppid = window.localStorage.getItem("wxAppid");
            const wxOpenid = window.localStorage.getItem("wxOpenid");
            const alipayUserId = window.localStorage.getItem("alipayUserId");
            let remark2 = "";
          
            if (navigator.userAgent.toLowerCase().indexOf("micromessenger") > -1 && wxAppid && wxOpenid) {
              remark2 = JSON.stringify({
                openid: wxOpenid,
                appid: wxAppid
              });
            } else if (navigator.userAgent.indexOf("AliApp") > -1 && alipayUserId) {
              remark2 = JSON.stringify({
                openid: alipayUserId
              });
            } else {
              showToast("未获取到用户信息");
              return;
            }
          
            console.log("remark2remark2remark2remark2", remark2);
          
            // 创建订单
            const params = {
              cartItemIds: [Number(cartData)],
              callBackUrl: baseUrl,
              remark2,
              doctorId: route.query.doctorId
            };
          
            const res = await createOrder(params);
            if (res.resultCode === 200 && res.data?.cashierUrl) {
              window.location.href = res.data.cashierUrl;
            } else {
              showToast("拉起收银台失败");
            }
            } catch (error) {
              showToast("下单失败");
            }
    }else{
        state.showDoctorPopup = true;
    }
  }
}

onMounted(async () => {
  const { id } = route.params
  if(route.query.doctorId){
    state.selectedDoctor = route.query.doctorId
    state.selectedDeptId = route.query.deptId
    state.unicode = route.query.unicode
  }
  try {
    // 获取服务包详情
    const { data: packageData } = await getServicePackageDetail(id)
    // 映射API返回数据到页面状态
    state.packageDetail = {
      id: packageData.goodsId,
      goodsName: packageData.goodsName,
      totalSales: packageData.goodsSellCount || 0,
      price: packageData.goodsSellingPrice || 0,
      description: packageData.goodsDetailContent || '',
      validityPeriod: packageData.goodsValidPeriod || '永久有效',
      goodsSpecification: packageData.specAndAttr || '',
      goodsDetailContent: packageData.goodsDetailContent || '',
      serviceItems: packageData.services.map(item => ({
        name: item.serviceName,
        count: item.serviceUsageCount
      })),
      giftItems: packageData.giftServices.map(item => ({
        name: item.serviceName,
        count: item.serviceUsageCount
      })),
      
    }
    state.doctorList = packageData.doctors
    state.unicode = packageData.unicode
    console.log('packageData', packageData)
    //查询规格信息
    // const { data: specsData } = await getServicePackageSpecs(id)
    // state.specs = specsData

    // 获取医生列表
    // const { data: doctorData } = await getServicePackageDoctors(id)
    // state.doctorList = doctorData
    
    console.log('服务包ID:', id)
  } catch (error) {
    console.error('获取服务包详情失败:', error)
    showToast('获取服务包详情失败')
  }
})
</script>

<style lang="less" scoped>
@import '../common/style/mixin';

/* 全局滚动优化 */
* {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.service-package-detail {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch; /* iOS 滑动流畅 */

  .detail-content {
    padding-bottom: 140px; /* 为固定栏预留空间 */
    background-color: white; /* 设置背景颜色为白色 */

  .banner-image {
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: auto; /* 改为auto使高度自适应 */
        object-fit: contain; /* 改为contain保持完整显示 */
        display: block;
      }
    }
  }

  .package-info {
    background-color: #fff;
    padding: 15px;
    
      .package-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        line-height: 1.4;
      }

      
      .package-sales {
      font-size: 12px;
      color: #999;
      margin-top: 8px;
    }
  }
  .main-content {
    padding: 0 15px;
    background-color: #fff;
  }

.content-card {
  background: linear-gradient(to bottom, #FFFAEE, #ffff);
  border-radius: 15px 15px 0 0;
  margin-bottom: 10px;
  /* 移除overflow:hidden以允许内容完整显示 */

  > * {
    margin: 15px;
  }

  > .section-title {
    margin: 0;
  }

  .section-title {
    /* ...之前的样式保持不变... */
      position: relative; /* <--- 关键：为伪元素定位提供基准 */
      display: inline-block;
      padding: 6px 40px 6px 18px;
      background: linear-gradient(to right, #FCE6D5, #FCF2E4);

      color: #BA6912;
      font-size: 14px;
      font-weight: 500;
      border-top-left-radius: 15px;
      /* 左下角圆角现在由伪元素来模拟，所以主元素可以取消 */
      border-bottom-left-radius: 0; /* <--- 修改：取消主元素的左下圆角 */
      clip-path: polygon(0 0, 100% 0, 85% 120%, 0 100%);

  }
  .section-title::before {
  content: ''; /* 伪元素必须有 content 属性 */
  position: absolute; /* <--- 关键：绝对定位 */
  bottom: -6px; /* <--- 定位到父元素下方 */
  left: 0;
  
  /* 定义伪元素的尺寸 */
  width: 6px; 
  height: 6px;

  /* 使用径向渐变来创建带有阴影的折角颜色
    第一个颜色是较暗的折角颜色，第二个是透明色，形成模糊边缘
  */
  background: radial-gradient(
    circle at top left, 
    transparent 70%, 
    rgba(169, 139, 100, 0.5) 71%, /* 模拟阴影的颜色，应比主背景深 */
    rgba(169, 139, 100, 0.8) 90%
  );
  
  /* 将伪元素旋转45度，并调整位置来对齐边角 */
  transform: rotate(45deg) translateX(-3px) translateY(-3px);
  
  /* 确保伪元素在主元素的下方 */
  z-index: -1; 
}

  .service-list, .gift-list {
    .service-item, .gift-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      .service-name, .gift-name {
        font-size: 14px;
        color: #333;
      }
      .service-count, .gift-count {
        font-size: 14px;
        color: #999;
      }
    }
  }
}
.service-description {
    padding: 15px;
    font-size: 14px;
    line-height: 1.6;
    color: #333;

    p {
      margin-bottom: 10px;
    }

    img {
      max-width: 100%;
      height: auto;
      margin: 10px 0;
    }
  }
  /* 固定的有效期栏样式 */
  .validity-bar {
    position: fixed;
    bottom: 68px; /* 在底部栏上方 */
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(to bottom, #FFFAEE,#FBF0DF);
    background: linear-gradient(to right, #FCE8D8, #FBF0DF);
    border-radius: 15px 15px 0 0;
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.1);
    z-index: 999;

    .period-label {
      font-size: 14px;
      color: #BA6912;
      font-weight: 500;
    }

    .period-value {
      font-size: 14px;
      color: #BA6912;
      font-weight: 600;
      margin-left: 5px;
    }
  }

.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 68px;
    background: #fff;
    display: flex;
    align-items: flex-start; /* 改动1：让直接子元素顶部对齐 */
    justify-content: space-between;
    padding: 8px 15px; /* 增加一些内边距，避免元素贴死在边上 */
    box-sizing: border-box; /* 让 padding 不会撑大高度 */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 1000;
}

/* --- 新增的核心样式 --- */
.bottom-bar .price-section {
    display: flex;
    align-items: flex-start
    /* 如果你的价格区域还有其他元素，可以继续在这里调整 */
}
/* -------------------- */

.bottom-bar .current-price {
    font-size: 24px;
    font-weight: 600;
    color: #FA541C;
    /* 如果希望价格和按钮在视觉上高度一致，可以调整行高 */
    line-height: 40px; 
}

.bottom-bar .total-price {
    font-size: 14px;
    margin-left: 10px;
    /* 如果使用了 baseline 对齐，可能需要微调这个元素的位置 */
    align-items: flex-baseline
}

.bottom-bar .total-price .price {
    font-family: PingFang SC;
    font-size: 24px;
    font-weight: 600;
    color: #fa541c;
    line-height: 24px;
}

.bottom-bar .total-price .price::before {
    content: "¥";
    font-size: 14px;
    font-weight: 600;
    margin-right: 2px;
}

.bottom-bar .total-price .price .decimal {
    font-size: 14px;
    font-weight: 600;
}

.bottom-bar .buy-button {
    width: 120px;
    height: 40px;
    background: linear-gradient(90deg, #ff7430 0%, #ff9d2e 100%);
    border-radius: 20px;
    border: none;
    margin-right: 5px;
    /* 为了让按钮里的文字垂直居中 */
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 16px;
    cursor: pointer;
}

  .doctor-selection {
    padding: 20px 15px;
    
    .popup-title {
      text-align: center;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 20px;
    }
    
    .doctor-list {
      max-height: 400px;
      overflow-y: auto;
      padding: 0 0;
      .doctor-item {
        background: #f4f7f7;
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-radius: 15px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 10px;
        cursor: pointer;
        
        &:last-child {
          border-bottom: none;
          margin-bottom: 0; /* 最后一项移除底部外边距 */
        }
        
        .doctor-avatar {
          width: 50px;
          height: 50px;
          border-radius: 25px;
          overflow: hidden;
          margin-right: 15px;
          margin-left:  15px; 
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        
        .doctor-info {
          flex: 1;
          .name-title-row {
                display: flex;
                align-items: center; /* 垂直居中对齐，让名字和标签在一条线上 */
                margin-bottom: 8px;
            }

            .doctor-name {
                font-family: Source Han Sans SC;
                font-weight: bold;
                font-size: 17px;
                color: #000000;
                line-height: 22px;
                color: #333;
                margin-bottom: 4px;
            }

            /* --- 为你的新标签添加样式 --- */
            .doctor-tag {
                background: #FDF3E0;
                color: #A77D2A;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 15px;
                margin-left: 8px;
                display: inline-block;  
            }
          .doctor-department {
            font-size: 12px;
            color: #999;
            margin-bottom: 2px;
          }
        }
        
      }
    }
  }
}
</style>
