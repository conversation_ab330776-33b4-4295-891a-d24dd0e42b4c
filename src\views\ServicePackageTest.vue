<!--
 * 服务包测试页面 - 用于测试跳转到服务包详情页面
-->

<template>
  <div class="service-package-test">
    <s-header :name="'服务包测试'"></s-header>
    
    <div class="test-content">
      <div class="test-section">
        <h3>从服务包列表进入（需要选择医生）</h3>
        <div class="service-package-list">
          <div 
            v-for="pkg in servicePackages" 
            :key="pkg.id"
            class="package-item"
            @click="goToPackageDetail(pkg.id)"
          >
            <div class="package-info">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">¥{{ pkg.price }}</div>
              <div class="package-sales">销量 {{ pkg.sales }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>从医生主页进入（直接购买）</h3>
        <div class="doctor-packages">
          <div class="doctor-info">
            <div class="doctor-name">张医生</div>
            <div class="doctor-title">主任医师</div>
          </div>
          <div class="doctor-package-list">
            <div 
              v-for="pkg in servicePackages" 
              :key="'direct-' + pkg.id"
              class="package-item"
              @click="goToPackageDetailDirect(pkg.id, 'doctor123')"
            >
              <div class="package-info">
                <div class="package-name">{{ pkg.name }}</div>
                <div class="package-price">¥{{ pkg.price }}</div>
                <div class="package-sales">销量 {{ pkg.sales }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import sHeader from '@/components/SimpleHeader.vue'

const router = useRouter()

const servicePackages = reactive([
  {
    id: 1,
    name: '美容护理套餐A',
    price: 300.00,
    sales: '2000+'
  },
  {
    id: 2,
    name: '美容护理套餐B',
    price: 500.00,
    sales: '1500+'
  },
  {
    id: 3,
    name: '美容护理套餐C',
    price: 800.00,
    sales: '1000+'
  }
])

// 跳转到服务包详情页面（需要选择医生）
const goToPackageDetail = (packageId) => {
  router.push({
    path: `service-package/11072?unicode=123301004701166305`
  })
}

// 跳转到服务包详情页面（直接购买）
const goToPackageDetailDirect = (packageId, doctorId) => {
  router.push({
    path: `/service-package/${packageId}`,
    query: {
      doctorId: doctorId
    }
  })
}
</script>

<style lang="less" scoped>
@import '../common/style/mixin';

.service-package-test {
  min-height: 100vh;
  background: #f5f5f5;
  
  .test-content {
    padding: 15px;
  }

  .test-section {
    background: #fff;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    
    h3 {
      background: #01CDA7;
      color: #fff;
      margin: 0;
      padding: 15px;
      font-size: 16px;
      font-weight: 600;
    }
    
    .service-package-list,
    .doctor-package-list {
      padding: 15px;
    }
    
    .doctor-info {
      padding: 15px;
      border-bottom: 1px solid #f0f0f0;
      
      .doctor-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
      }
      
      .doctor-title {
        font-size: 14px;
        color: #666;
      }
    }
    
    .package-item {
      background: #f8f8f8;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #e8f5f3;
        transform: translateY(-2px);
      }
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .package-info {
        .package-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }
        
        .package-price {
          font-size: 18px;
          font-weight: 600;
          color: #FA541C;
          margin-bottom: 5px;
        }
        
        .package-sales {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}
</style>
